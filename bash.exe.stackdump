Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBF80, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 0007FFFFBF80, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA9D460000 ntdll.dll
7FFA9B870000 KERNEL32.DLL
7FFA9A5B0000 KERNELBASE.dll
7FFA9B2C0000 USER32.dll
7FFA9AF30000 win32u.dll
7FFA9C110000 GDI32.dll
7FFA9ADF0000 gdi32full.dll
7FFA9ABC0000 msvcp_win.dll
7FFA9AF60000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA9C5F0000 advapi32.dll
7FFA9C060000 msvcrt.dll
7FFA9B7C0000 sechost.dll
7FFA9C140000 RPCRT4.dll
7FFA99AC0000 CRYPTBASE.DLL
7FFA9A9A0000 bcryptPrimitives.dll
7FFA9C010000 IMM32.DLL
