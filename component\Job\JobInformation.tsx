import glob from '../../public/images/icon/globe.svg'
import facebook from '../../public/images/icon/facebook_ic_green.svg'
import twitter from '../../public/images/icon/twitter_ic_green.svg'
import instagram from '../../public/images/icon/instagram_ic_green.svg'
import linkedin from '../../public/images/icon/linkedIn_ic_green.svg'
import JobAction from './JobAction'
import styles from '../../styles/JobInformations.module.scss'
import { numberWithCommas } from '../../hook/numberWithCommas'

// @ts-ignore
const JobInformation = ({ data, permissions, token, job }) => {

  return (
    <div className={styles.information}>
      <div className={styles.information__inner}>
        <div className={styles.information__head}>
          <p className={styles.information__head__headline}>
            Key Job Details
          </p>
          {/*//TODO: need to add when matched logic when has been created(candidate/recruiter)*/}
          {/*<Link href="/">*/}
          {/*  <a className={styles.information__head__link}>*/}
          {/*    See all matches: <span>324</span>*/}
          {/*  </a>*/}
          {/*</Link>*/}
        </div>
        <ul className={styles.information__list}>
          <li className={styles.information__item}>
            <span>Location: </span>
            <span className={styles.information__item__location}>
            {
              data?.locations && data.locations.map((item: any) => {
                return item.city+', ' + item.state+'. '
              })
            }
            </span>
          </li>
          <li className={styles.information__item}>
            <span>Salary: </span>${ numberWithCommas(data?.salaryYearMin) } - ${numberWithCommas(data?.salaryYearMin)} PA
          </li>
          <li className={styles.information__item}>
            <span>Experience: </span>{data?.experienceMin}-{data?.experienceMax} years
          </li>
          <li className={styles.information__item}>
            <span>Education: </span>{data?.education}
          </li>
          {
            data?.jobType && (
              <li className={styles.information__item}>
                <span>Job Type: </span>{data?.jobType}
              </li>
            )
          }
          {
            data?.preferableShift && (
              <li className={styles.information__item}>
                <span>Preferable Shift: </span>{data?.preferableShift}
              </li>
            )
          }
          {
            data?.industryType && (
              <li className={styles.information__item}>
                <span>Industry Type: </span>{data?.industryType}
              </li>
            )
          }
          {
            data?.jobType && (
              <li className={styles.information__item}>
                <span>Functional Area: </span>{data?.functionalArea}
              </li>
            )
          }
        </ul>
        <JobAction
          color="black"
          permissions={permissions}
          token={token}
          job={job}
        />
      </div>
      <div className={styles.information__social}>
        <div className={styles.information__social__left}>
          {
            data?.company.website && (
              <div className={styles.information__social__website}>
                <img src={glob.src} alt="icon" className={`${styles.information__social__icon} ${styles.small}`}/>
                <span className={styles.information__social__name}>company website:</span>
                <a href={data.company.website} className={styles.information__social__link} target="_blank">
                  {data.company.website}
                </a>
              </div>
            )
          }
          {
            data?.careerPortal && (
              <div className={styles.information__social__website}>
                <img src={glob.src} alt="icon" className={`${styles.information__social__icon} ${styles.small}`}/>
                <span className={styles.information__social__name}>career portal:</span>
                <a href={data.careerPortal} className={styles.information__social__link} target="_blank">
                  {data.careerPortal}
                </a>
              </div>
            )
          }
        </div>
        <div className={styles.information__social__right}>
          {/*//TODO: need to add url validation when we create job*/}
          {
            data?.facebook.length > 0 && (
              <a href={data.facebook} className={styles.information__social__item}>
                <img src={facebook.src} alt="facebook"/>
              </a>
            )
          }
          {
            data?.twitter.length > 0 && (
              <a href={data.twitter} className={styles.information__social__item}>
                <img src={twitter.src} alt="twitter"/>
              </a>
            )
          }
          {
            data?.instagram.length > 0 && (
              <a href={data.instagram} className={styles.information__social__item}>
                <img src={instagram.src} alt="instagram"/>
              </a>
            )
          }
          {
            data?.linkedin.length > 0 && (
              <a href={data.linkedin} className={styles.information__social__item}>
                <img src={linkedin.src} alt="linkedin"/>
              </a>
            )
          }
        </div>
      </div>
    </div>
  )
}

export default JobInformation