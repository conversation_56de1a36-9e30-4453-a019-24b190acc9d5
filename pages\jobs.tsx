import styles from "../styles/Jobs.module.scss";
import globalStyle from "../styles/Global.module.scss";
import { default as ReactSelect } from "react-select";
import Head from "next/head";
import Layout from "../component/Layout";
import { useState } from "react";
import JobCard from "../component/Jobs/JobCard";
import JobFilter, { toJSONLocal, toJSONLocalPlusDay } from "../component/Jobs/JobFilter";
import { selectSmallStyle } from "../public/files/selectSmallStyle";
import filterIc from "../public/images/icon/filter_ic.svg";
import GetStarted from "../component/GetStarted";
import ContactUs from "../component/ContactUs";
import { IAllJobs, ServicesPageInterface } from "../interfaces/HomePageInterfaces";
import { NextPage } from "next";
import getToken from "../utils/getToken";
import Pagination from "../component/Pagination";
import axios from "axios";
import { store } from "../store";
import { changeLimit } from "../store/action-creator/jobs";
import { useTypedSelector } from "../hook/useTypedSelector";
import { useRouter } from "next/router";
import { getAccessToken } from "@auth0/nextjs-auth0";
import ApplyPopup from "../component/Job/ApplyPopup";
import SuccessfullyApplyPopup from "../component/Job/SuccessfullyApplyPopup";
import getNoDecodeToken from "../utils/getNoDecodeToken";

const countOptions = [
	{ value: 1, label: "1" },
	{ value: 2, label: "2" },
	{ value: 10, label: "10" },
	{ value: 15, label: "15" },
	{ value: 20, label: "20" },
	{ value: 25, label: "25" },
];

interface Data {
	services: ServicesPageInterface;
	jobs: IAllJobs;
	permissions: boolean;
	token: any;
	companyId:number;
	isCandidate:boolean;
}

const JobPage: NextPage<Data> = (props) => {
	const initData = useTypedSelector(state => state.jobs);
	const { applyPopup, successApplyPopup } = useTypedSelector(state => state.app);

	const {
		jobTitle,
		jobLocation,
		postedOnData,
		salaryYearValue,
		salaryMonthValue,
		jobType,
		experienceYearValue,
		skills,
		functionalArea,
		industryType,
		education,
		companyName,
		preferableShift,
	} = useTypedSelector(state => state.jobs.filters);
	const router = useRouter();

	const [activeActionTab, setActiveActionTab] = useState(1);
	const [displayFormat, setDisplayFormat] = useState("tile");
	const [displayFilter, setDisplayFilter] = useState(false);

	const onActiveTabChange = (tab:number)=>{
		setActiveActionTab(tab);
		router.push(decodeURI(`/jobs/?page=1
			&limit=${initData.limit}
			&title=${jobTitle}
			${jobLocation ? jobLocation.map((x: any) => `&locations=${x.id}`).join("") : ""}
			&createdAtFrom=${postedOnData ? toJSONLocal(postedOnData) : ""}
			&createdAtTo=${postedOnData ? toJSONLocalPlusDay(postedOnData) : ""}
			&salaryMonthMin=${salaryMonthValue[0]}
			&salaryMonthMax=${salaryMonthValue[1]}
			&salaryYearMin=${salaryYearValue[0]}
			&salaryYearMax=${salaryYearValue[1]}
			&experienceMin=${experienceYearValue[0]}
			&experienceMax=${experienceYearValue[1]}
			&education=${education?.value ? education?.value : ""}
			${skills ? skills.map((x: any) => `&skills=${x.label}`).join("") : ""}
			&companyId=${companyName?.id ? companyName?.id : ""}
			&jobType=${jobType?.value ? jobType?.value : ""}
			&industryId=${industryType?.id ? industryType?.id : ""}
			&functionalArea=${functionalArea?.value ? functionalArea?.value : ""}
			&preferableShift=${preferableShift?.label ? preferableShift?.label : ""}
			&filterType=${tab==2?'matched':tab==3?'saved':tab==4?'applied':''}
			`));

	}

	const onLimitChange = (value: number) => {
		router.push(decodeURI(`/jobs/?page=1
		&limit=${value}
		&title=${jobTitle}
		${jobLocation ? jobLocation.map((x: any) => `&locations=${x.id}`).join("") : ""}
		&createdAtFrom=${postedOnData ? toJSONLocal(postedOnData) : ""}
		&createdAtTo=${postedOnData ? toJSONLocalPlusDay(postedOnData) : ""}
		&salaryMonthMin=${salaryMonthValue[0]}
		&salaryMonthMax=${salaryMonthValue[1]}
		&salaryYearMin=${salaryYearValue[0]}
		&salaryYearMax=${salaryYearValue[1]}
		&experienceMin=${experienceYearValue[0]}
		&experienceMax=${experienceYearValue[1]}
		&education=${education?.value ? education?.value : ""}
		${skills ? skills.map((x: any) => `&skills=${x.label}`).join("") : ""}
		&companyId=${companyName?.id ? companyName?.id : ""}
		&jobType=${jobType?.value ? jobType?.value : ""}
		&industryId=${industryType?.id ? industryType?.id : ""}
		&functionalArea=${functionalArea?.value ? functionalArea?.value : ""}
		&preferableShift=${preferableShift?.label ? preferableShift?.label : ""}
		&filterType=${activeActionTab==2?'matched':activeActionTab==3?'saved':activeActionTab==4?'applied':''}
		`));
	};

	return (
		<Layout>
			<Head>
				<title>Find Jobs & Career Opportunities | Apply Here - uRecruits</title>
				<meta name="description" content="Explore the latest job openings across industries. Discover full-time, part-time, and remote opportunities tailored to your skills and goals." />
			</Head>
			<section className={styles.jobs}>
				<div className={styles.jobs__inner}>
					<div className={styles.jobs__head}>
						<p className={`${styles.jobs__tagline} ${globalStyle.tagline}`}>
							Jobs marketplace
						</p>
						<h1 className={styles.jobs__headline}>
							Find a job that you like
						</h1>
						<div className={styles.jobs__action}>
							<div className={styles.jobs__action__left}>
								<div className={styles.jobs__action__list}>
									<div className={`${styles.jobs__action__item} ${activeActionTab === 1 && (styles.active)}`} onClick={()=>onActiveTabChange(1)}>
										<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"
											className={styles.jobs__action__img}>
											<path
												d="M16.668 5.83447H3.33464C2.41416 5.83447 1.66797 6.58066 1.66797 7.50114V15.8345C1.66797 16.7549 2.41416 17.5011 3.33464 17.5011H16.668C17.5884 17.5011 18.3346 16.7549 18.3346 15.8345V7.50114C18.3346 6.58066 17.5884 5.83447 16.668 5.83447Z"
												stroke="#343B43" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
											<path
												d="M13.3346 17.5V4.16667C13.3346 3.72464 13.159 3.30072 12.8465 2.98816C12.5339 2.67559 12.11 2.5 11.668 2.5H8.33464C7.89261 2.5 7.46869 2.67559 7.15612 2.98816C6.84356 3.30072 6.66797 3.72464 6.66797 4.16667V17.5"
												stroke="#343B43" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
										</svg>
										<p className={styles.jobs__action__name}>
											All Jobs
										</p>
									</div>
									{
										props.isCandidate && (
											<>
												<div className={`${styles.jobs__action__item} ${activeActionTab === 2 && (styles.active)}`} onClick={()=>onActiveTabChange(2)}>
													<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"
														className={styles.jobs__action__img}>
														<path
															d="M1.66797 19.3356V3.50226H5.92722C5.92722 1.94907 7.36242 1 8.7513 1C10.1402 1 11.8531 1.94907 11.8531 3.50226L17.5013 3.50224V7.99297C15.9041 7.99297 15.0013 9.33333 15.0013 11C15.0013 12.6667 15.9041 13.9189 17.5013 13.9189V19.3355L1.66797 19.3356Z"
															stroke="#343B43" strokeWidth="1.25" strokeLinejoin="round" />
														<mask id="path-2-inside-1_6969_83801" fill="white">
															<path
																d="M5 8.80841C5 8.28371 5.16947 7.85113 5.50842 7.51068C5.85145 7.17023 6.29045 7 6.82542 7C7.36856 7 7.80755 7.17223 8.14242 7.51669C8.48137 7.85714 8.65084 8.29973 8.65084 8.84446V9.27103C8.65084 9.79973 8.47933 10.2323 8.13629 10.5688C7.79735 10.9012 7.36447 11.0674 6.83767 11.0674C6.30679 11.0674 5.86779 10.9012 5.52067 10.5688C5.17356 10.2323 5 9.78571 5 9.22897V8.80841ZM5.84533 9.27103C5.84533 9.58344 5.93517 9.84179 6.11485 10.0461C6.29454 10.2463 6.53548 10.3465 6.83767 10.3465C7.12762 10.3465 7.36039 10.2483 7.53599 10.0521C7.71567 9.85581 7.80551 9.58745 7.80551 9.247V8.80841C7.80551 8.49599 7.71771 8.23765 7.54211 8.03338C7.36651 7.82911 7.12762 7.72697 6.82542 7.72697C6.52323 7.72697 6.28433 7.82911 6.10873 8.03338C5.93313 8.23765 5.84533 8.50401 5.84533 8.83244V9.27103ZM9.34303 13.729C9.34303 13.2043 9.51251 12.7737 9.85146 12.4372C10.1945 12.0968 10.6335 11.9266 11.1685 11.9266C11.7034 11.9266 12.1424 12.0948 12.4855 12.4312C12.8285 12.7677 13 13.2143 13 13.771V14.1976C13 14.7223 12.8285 15.1549 12.4855 15.4953C12.1465 15.8318 11.7116 16 11.1807 16C10.6498 16 10.2108 15.8338 9.86371 15.5013C9.51659 15.1649 9.34303 14.7183 9.34303 14.1615V13.729ZM10.1884 14.1976C10.1884 14.514 10.2782 14.7744 10.4579 14.9786C10.6376 15.1789 10.8785 15.279 11.1807 15.279C11.4747 15.279 11.7095 15.1809 11.8851 14.9846C12.0607 14.7844 12.1485 14.514 12.1485 14.1736V13.729C12.1485 13.4125 12.0587 13.1542 11.879 12.9539C11.7034 12.7537 11.4666 12.6535 11.1685 12.6535C10.8785 12.6535 10.6417 12.7537 10.4579 12.9539C10.2782 13.1502 10.1884 13.4166 10.1884 13.753V14.1976ZM7.09495 15.213L6.45176 14.8164L10.807 7.97931L11.4502 8.37583L7.09495 15.213Z" />
														</mask>
														<path
															d="M5.50842 7.51068L4.45175 6.44601L4.44541 6.45237L5.50842 7.51068ZM8.14242 7.51669L7.06689 8.56226C7.07104 8.56653 7.07521 8.57078 7.07941 8.575L8.14242 7.51669ZM5.52067 10.5688L4.47667 11.6459L4.48316 11.6521L5.52067 10.5688ZM6.11485 10.0461L4.98858 11.0368C4.99182 11.0405 4.99509 11.0441 4.99837 11.0478L6.11485 10.0461ZM7.53599 10.0521L6.42963 9.03917C6.42577 9.04338 6.42193 9.04762 6.41812 9.05189L7.53599 10.0521ZM9.85146 12.4372L8.7948 11.3726L8.79473 11.3727L9.85146 12.4372ZM12.4855 15.4953L11.4288 14.4307L11.4287 14.4307L12.4855 15.4953ZM9.86371 15.5013L8.81971 16.5784L8.82619 16.5846L9.86371 15.5013ZM10.4579 14.9786L9.33161 15.9693C9.33486 15.973 9.33812 15.9767 9.3414 15.9804L10.4579 14.9786ZM11.8851 14.9846L13.003 15.9848C13.0064 15.9811 13.0097 15.9773 13.013 15.9736L11.8851 14.9846ZM11.879 12.9539L10.7512 13.9429C10.7549 13.9472 10.7587 13.9514 10.7625 13.9557L11.879 12.9539ZM10.4579 12.9539L9.35267 11.9398L9.35153 11.941L10.4579 12.9539ZM7.09495 15.213L6.30776 16.4898C7.00562 16.92 7.91961 16.7103 8.36007 16.0188L7.09495 15.213ZM6.45176 14.8164L5.18664 14.0105C4.97085 14.3493 4.89993 14.7604 4.98976 15.1519C5.0796 15.5434 5.32268 15.8825 5.66458 16.0933L6.45176 14.8164ZM10.807 7.97931L11.5942 6.70246C10.8964 6.27223 9.98238 6.48197 9.54192 7.17341L10.807 7.97931ZM11.4502 8.37583L12.7154 9.18173C12.9311 8.84297 13.0021 8.43182 12.9122 8.04034C12.8224 7.64886 12.5793 7.30977 12.2374 7.09899L11.4502 8.37583ZM6.5 8.80841C6.5 8.64016 6.53951 8.60105 6.57143 8.56899L4.44541 6.45237C3.79944 7.10122 3.5 7.92727 3.5 8.80841H6.5ZM6.56507 8.57533C6.59586 8.54478 6.63996 8.5 6.82542 8.5V5.5C5.94095 5.5 5.10705 5.79567 4.45177 6.44603L6.56507 8.57533ZM6.82542 8.5C7.02131 8.5 7.05242 8.54739 7.06689 8.56226L9.21795 6.47111C8.56269 5.79707 7.7158 5.5 6.82542 5.5V8.5ZM7.07941 8.575C7.1002 8.59587 7.15084 8.63861 7.15084 8.84446H10.1508C10.1508 7.96085 9.86254 7.11841 9.20543 6.45838L7.07941 8.575ZM7.15084 8.84446V9.27103H10.1508V8.84446H7.15084ZM7.15084 9.27103C7.15084 9.44142 7.11106 9.47325 7.08596 9.49787L9.18663 11.6396C9.84759 10.9914 10.1508 10.158 10.1508 9.27103H7.15084ZM7.08596 9.49787C7.05931 9.52401 7.01872 9.56742 6.83767 9.56742V12.5674C7.71023 12.5674 8.53538 12.2784 9.18663 11.6396L7.08596 9.49787ZM6.83767 9.56742C6.64934 9.56742 6.59581 9.52148 6.55819 9.48545L4.48316 11.6521C5.13977 12.2809 5.96423 12.5674 6.83767 12.5674V9.56742ZM6.56465 9.49168C6.54477 9.4724 6.53612 9.45824 6.5283 9.43813C6.51881 9.41371 6.5 9.35113 6.5 9.22897H3.5C3.5 10.1246 3.7932 10.9834 4.47669 11.6458L6.56465 9.49168ZM6.5 9.22897V8.80841H3.5V9.22897H6.5ZM4.34533 9.27103C4.34533 9.89054 4.53253 10.5183 4.98858 11.0368L7.24113 9.05536C7.28012 9.09968 7.31104 9.15336 7.32948 9.2064C7.34688 9.25642 7.34533 9.283 7.34533 9.27103H4.34533ZM4.99837 11.0478C5.49932 11.6061 6.16776 11.8465 6.83767 11.8465V8.84646C6.85324 8.84646 6.91747 8.84944 7.00598 8.88622C7.09867 8.92474 7.17656 8.98328 7.23134 9.04433L4.99837 11.0478ZM6.83767 11.8465C7.50617 11.8465 8.16307 11.6008 8.65386 11.0523L6.41812 9.05189C6.4718 8.99188 6.55021 8.93148 6.64701 8.89067C6.74031 8.85134 6.81186 8.84646 6.83767 8.84646V11.8465ZM8.64234 11.065C9.13997 10.5214 9.30551 9.85404 9.30551 9.247H6.30551C6.30551 9.28717 6.30004 9.27426 6.31573 9.22741C6.33314 9.17541 6.36885 9.10556 6.42963 9.03917L8.64234 11.065ZM9.30551 9.247V8.80841H6.30551V9.247H9.30551ZM9.30551 8.80841C9.30551 8.19783 9.12581 7.57463 8.6796 7.05556L6.40463 9.0112C6.36575 8.96597 6.33645 8.91304 6.31944 8.86301C6.30347 8.81603 6.30551 8.79296 6.30551 8.80841H9.30551ZM8.6796 7.05556C8.18232 6.47708 7.5051 6.22697 6.82542 6.22697V9.22697C6.80654 9.22697 6.7364 9.22335 6.64157 9.18281C6.54227 9.14035 6.46067 9.07639 6.40463 9.0112L8.6796 7.05556ZM6.82542 6.22697C6.14575 6.22697 5.46852 6.47708 4.97125 7.05556L7.24621 9.0112C7.19017 9.07639 7.10857 9.14035 7.00927 9.18281C6.91445 9.22335 6.84431 9.22697 6.82542 9.22697V6.22697ZM4.97125 7.05556C4.51336 7.58821 4.34533 8.22713 4.34533 8.83244H7.34533C7.34533 8.80113 7.34953 8.81421 7.33578 8.85595C7.32075 8.90152 7.29173 8.95824 7.24621 9.0112L4.97125 7.05556ZM4.34533 8.83244V9.27103H7.34533V8.83244H4.34533ZM10.843 13.729C10.843 13.5606 10.8823 13.5275 10.9082 13.5018L8.79473 11.3727C8.14269 12.0199 7.84303 12.8479 7.84303 13.729H10.843ZM10.9081 13.5019C10.9389 13.4713 10.983 13.4266 11.1685 13.4266V10.4266C10.284 10.4266 9.45008 10.7222 8.7948 11.3726L10.9081 13.5019ZM11.1685 13.4266C11.3592 13.4266 11.4054 13.473 11.4351 13.5021L13.5358 11.3604C12.8794 10.7166 12.0476 10.4266 11.1685 10.4266V13.4266ZM11.4351 13.5021C11.4445 13.5113 11.5 13.5478 11.5 13.771H14.5C14.5 12.8808 14.2125 12.024 13.5358 11.3604L11.4351 13.5021ZM11.5 13.771V14.1976H14.5V13.771H11.5ZM11.5 14.1976C11.5 14.3603 11.4623 14.3974 11.4288 14.4307L13.5421 16.56C14.1946 15.9123 14.5 15.0843 14.5 14.1976H11.5ZM11.4287 14.4307C11.4033 14.456 11.364 14.5 11.1807 14.5V17.5C12.0591 17.5 12.8897 17.2076 13.5422 16.5599L11.4287 14.4307ZM11.1807 14.5C10.9924 14.5 10.9388 14.4541 10.9012 14.418L8.82619 16.5846C9.4828 17.2135 10.3073 17.5 11.1807 17.5V14.5ZM10.9077 14.4243C10.8878 14.405 10.8791 14.3908 10.8713 14.3707C10.8618 14.3463 10.843 14.2837 10.843 14.1615H7.84303C7.84303 15.0572 8.13623 15.9159 8.81973 16.5784L10.9077 14.4243ZM10.843 14.1615V13.729H7.84303V14.1615H10.843ZM8.68836 14.1976C8.68836 14.8156 8.87244 15.4473 9.33161 15.9693L11.5842 13.9879C11.6249 14.0342 11.6557 14.0889 11.6737 14.1409C11.6905 14.1896 11.6884 14.2137 11.6884 14.1976H8.68836ZM9.3414 15.9804C9.84235 16.5387 10.5108 16.779 11.1807 16.779V13.779C11.1963 13.779 11.2605 13.782 11.349 13.8188C11.4417 13.8573 11.5196 13.9159 11.5744 13.9769L9.3414 15.9804ZM11.1807 16.779C11.8461 16.779 12.5079 16.5382 13.003 15.9848L10.7673 13.9845C10.8231 13.922 10.9029 13.8616 10.9989 13.8214C11.091 13.783 11.1597 13.779 11.1807 13.779V16.779ZM13.013 15.9736C13.4892 15.4304 13.6485 14.7738 13.6485 14.1736H10.6485C10.6485 14.2166 10.6428 14.209 10.656 14.1684C10.6707 14.1231 10.702 14.0588 10.7573 13.9957L13.013 15.9736ZM13.6485 14.1736V13.729H10.6485V14.1736H13.6485ZM13.6485 13.729C13.6485 13.1118 13.4646 12.475 12.9955 11.9522L10.7625 13.9557C10.7173 13.9052 10.6837 13.8461 10.6644 13.7906C10.6463 13.7387 10.6485 13.7127 10.6485 13.729H13.6485ZM13.0069 11.965C12.5106 11.399 11.841 11.1535 11.1685 11.1535V14.1535C11.1485 14.1535 11.079 14.1498 10.9856 14.1102C10.8879 14.0689 10.8072 14.0067 10.7512 13.9429L13.0069 11.965ZM11.1685 11.1535C10.4898 11.1535 9.84116 11.4074 9.35268 11.9398L11.5631 13.9681C11.5181 14.0172 11.4483 14.0719 11.3574 14.1102C11.2689 14.1477 11.1986 14.1535 11.1685 14.1535V11.1535ZM9.35153 11.941C8.85679 12.4814 8.68836 13.1445 8.68836 13.753H11.6884C11.6884 13.7167 11.6933 13.732 11.6771 13.7803C11.6591 13.8336 11.6233 13.9024 11.5642 13.9668L9.35153 11.941ZM8.68836 13.753V14.1976H11.6884V13.753H8.68836ZM7.88213 13.9361L7.23895 13.5396L5.66458 16.0933L6.30776 16.4898L7.88213 13.9361ZM7.71689 15.6223L12.0722 8.7852L9.54192 7.17341L5.18664 14.0105L7.71689 15.6223ZM10.0199 9.25615L10.663 9.65268L12.2374 7.09899L11.5942 6.70246L10.0199 9.25615ZM10.1851 7.56994L5.82982 14.4071L8.36007 16.0188L12.7154 9.18173L10.1851 7.56994Z"
															fill="#343B43" mask="url(#path-2-inside-1_6969_83801)" />
													</svg>
													<p className={styles.jobs__action__name}>
														Matched Jobs
													</p>
												</div>
												<div className={`${styles.jobs__action__item} ${activeActionTab === 3 && (styles.active)}`}  onClick={()=>onActiveTabChange(3)}>
													<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"
														className={styles.jobs__action__img}>
														<path
															d="M16.6654 18.3334L9.9987 13.7038L3.33203 18.3334V3.5186C3.33203 3.02746 3.53271 2.55643 3.88992 2.20914C4.24714 1.86185 4.73162 1.66675 5.23679 1.66675H14.7606C15.2658 1.66675 15.7503 1.86185 16.1075 2.20914C16.4647 2.55643 16.6654 3.02746 16.6654 3.5186V18.3334Z"
															stroke="#343B43" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
													</svg>
													<p className={styles.jobs__action__name}>
														Saved Jobs
													</p>
												</div>
												<div className={`${styles.jobs__action__item} ${activeActionTab === 4 && (styles.active)}`}  onClick={()=>onActiveTabChange(4)}>
													<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"
														className={styles.jobs__action__img}>
														<path
															d="M18.3346 11V7.50114C18.3346 6.58066 17.5884 5.83447 16.668 5.83447H3.33464C2.41416 5.83447 1.66797 6.58066 1.66797 7.50114V15.8345C1.66797 16.7549 2.41416 17.5011 3.33464 17.5011H10.0013"
															stroke="#343B43" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
														<path
															d="M13.3346 8.5V4.16667C13.3346 3.72464 13.159 3.30072 12.8465 2.98816C12.5339 2.67559 12.11 2.5 11.668 2.5H8.33464C7.89261 2.5 7.46869 2.67559 7.15612 2.98816C6.84356 3.30072 6.66797 3.72464 6.66797 4.16667V17.5"
															stroke="#343B43" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
														<circle cx="14" cy="14" r="5.25" stroke="#343B43" strokeWidth="1.5" />
														<path d="M14 12V16" stroke="#343B43" strokeWidth="1.5" strokeLinecap="round"
															strokeLinejoin="round" />
														<path d="M16 14H12" stroke="#343B43" strokeWidth="1.5" strokeLinecap="round"
															strokeLinejoin="round" />
													</svg>
													<p className={styles.jobs__action__name}>
														Applied to
													</p>
												</div>
											</>
										)
									}
								</div>
							</div>
							<div className={styles.jobs__action__right}>
								<div className={styles.jobs__filterBtn} onClick={() => setDisplayFilter(!displayFilter)}>
									<img src={filterIc.src} alt="icon" className={styles.jobs__filterBtn__icon} />
									<p className={styles.jobs__filterBtn__text}>
										Filter
									</p>
								</div>
								<div className={styles.jobs__show}>
									<p className={styles.jobs__show__text}>
										Show
									</p>
									<div className={styles.jobs__show__select}>
										<ReactSelect
											options={countOptions}
											closeMenuOnSelect={true}
											hideSelectedOptions={false}
											onChange={(item: any) => {
												onLimitChange(item.value);
												store.dispatch(changeLimit(item.value));
											}}
											value={{ value: initData.limit, label: initData.limit }}
											isSearchable={false}
											defaultValue={{ label: "10", value: 10 }}
											styles={selectSmallStyle}
											id="countOptionSelect"
											instanceId="countOptionSelect"
										/>
									</div>
									<p className={styles.jobs__show__text}>
										on page
									</p>
								</div>
								<div className={styles.jobs__format}>
									<div
										className={`${styles.jobs__format__item} ${displayFormat === "list" && (styles.active)}`}
										onClick={() => setDisplayFormat("list")}
									>
										<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path d="M8 6H21" stroke="#999EA5" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
											<path d="M8 12H21" stroke="#999EA5" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
											<path d="M8 18H21" stroke="#999EA5" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
											<path d="M3 6H3.01" stroke="#999EA5" strokeWidth="2" strokeLinecap="round"
												strokeLinejoin="round" />
											<path d="M3 12H3.01" stroke="#999EA5" strokeWidth="2" strokeLinecap="round"
												strokeLinejoin="round" />
											<path d="M3 18H3.01" stroke="#999EA5" strokeWidth="2" strokeLinecap="round"
												strokeLinejoin="round" />
										</svg>
									</div>
									<div
										className={`${styles.jobs__format__item} ${displayFormat === "tile" && (styles.active)}`}
										onClick={() => setDisplayFormat("tile")}
									>
										<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path d="M10 3H3V10H10V3Z" stroke="#999EA5" strokeWidth="1.5" strokeLinecap="round"
												strokeLinejoin="round" />
											<path d="M21 3H14V10H21V3Z" stroke="#999EA5" strokeWidth="1.5" strokeLinecap="round"
												strokeLinejoin="round" />
											<path d="M21 14H14V21H21V14Z" stroke="#999EA5" strokeWidth="1.5" strokeLinecap="round"
												strokeLinejoin="round" />
											<path d="M10 14H3V21H10V14Z" stroke="#999EA5" strokeWidth="1.5" strokeLinecap="round"
												strokeLinejoin="round" />
										</svg>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className={styles.jobs__body}>
						<div className={styles.jobs__body__left}>
							<JobFilter displayFilter={displayFilter} activeTab={activeActionTab} />
						</div>
						<div className={styles.jobs__body__right}>
							<p className={styles.jobs__count}>
								{/*We found {props.jobs.rows.length} jobs matching your criteria*/}
							</p>
							<div className={styles.jobs__list}>
								{
									props.jobs.rows.map((item, index) => {
										return (
											<JobCard
												data={item}
												key={index}
												displayFormat={displayFormat}
												permissions={props.permissions}
												token={props.token}
												companyId={props.companyId||0}
											/>
										);
									})
								}
							</div>
							{
								props.jobs.count > 1 && (
									<div className={styles.jobs__pagination}>
										<Pagination
											limit={initData.limit}
											currentPage={router.query.page && parseInt(router.query.page as string) || 1}
											totalCount={props.jobs.count}
										/>
									</div>
								)
							}
							{
								!props.jobs.count && (
									<p className={styles.jobs__count}>No jobs were found matching your criteria. Try adjusting the filters to see more options.</p>
								)
							}
						</div>
					</div>
				</div>
			</section>
			<GetStarted props={props.services} />
			<ContactUs />
			{applyPopup.visible && <ApplyPopup />}
			{successApplyPopup && <SuccessfullyApplyPopup />}
		</Layout>
	);
};

export default JobPage;

export async function getServerSideProps(context: { res: any, req: any, query: any }) {

	const {
		page,
		limit,
		title,
		locations,
		createdAtFrom,
		createdAtTo,
		salaryMonthMin,
		salaryMonthMax,
		salaryYearMin,
		salaryYearMax,
		experienceMin,
		experienceMax,
		education,
		skills,
		companyId,
		jobType,
		industryId,
		functionalArea,
		filterType
	} = context.query;

	const response_services = await fetch("https://cms-dev.urecruits.com/services");
	const services = await response_services.json();

	const userData = await getToken(context.req, context.res);
	const noDecodeToken = await getNoDecodeToken(context.req, context.res);

	const jobs: any = await axios(`${process.env.NEXT_PUBLIC_RECRUITMENT_API}/api/${(userData && filterType=='matched')? 'job/match/jobs':'job'}`, {
		...(filterType == 'matched' && { headers: {
			Authorization: `Bearer ${noDecodeToken}`,  // Add your token here
		  },}),
		params: {
			page: page || 1,
			locations: locations,
			limit: limit || 10,
			offset: page > 1 ? (page - 1) * limit || 10 : 0,
			title: title,
			createdAtFrom: createdAtFrom,
			createdAtTo: createdAtTo,
			salaryMonthMin: salaryMonthMin,
			salaryMonthMax: salaryMonthMax,
			salaryYearMin: salaryYearMin,
			salaryYearMax: salaryYearMax,
			experienceMin: experienceMin,
			experienceMax: experienceMax,
			education: education,
			skills: skills,
			companyId: companyId,
			jobType: jobType,
			industryId: industryId,
			functionalArea: functionalArea,
			...(filterType&& filterType !=='matched' && {filterType: filterType}),
			currentUserId: userData["https://urecruits.com/userId"] || null,
		},
	});
	return {
		props: {
			services: services,
			jobs: jobs.data,
			permissions: !!userData?.permissions?.includes("recruiter"),
			isCandidate: !!userData?.permissions?.includes("candidate"),
			token: !!noDecodeToken ? noDecodeToken : 'Empty',
			companyId:userData["https://urecruits.com/companyId"] || null,
		},
	};
}
